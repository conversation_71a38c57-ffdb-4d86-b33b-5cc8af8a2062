import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../constants/app_constants.dart';

class MainScreen extends ConsumerStatefulWidget {
  final Widget child;

  const MainScreen({super.key, required this.child});

  @override
  ConsumerState<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends ConsumerState<MainScreen> {
  int _calculateSelectedIndex(BuildContext context) {
    final String location = GoRouterState.of(context).uri.path;
    if (location == AppConstants.itemsRoute) {
      return AppConstants.itemsTabIndex;
    }
    if (location == AppConstants.newTransactionRoute) {
      return AppConstants.newTransactionTabIndex;
    }
    if (location == AppConstants.unpaidTransactionsRoute) {
      return AppConstants.unpaidTransactionsTabIndex;
    }
    if (location == AppConstants.transactionHistoryRoute) {
      return AppConstants.transactionHistoryTabIndex;
    }
    return 0;
  }

  void _onItemTapped(int index, BuildContext context) {
    switch (index) {
      case AppConstants.itemsTabIndex:
        context.go(AppConstants.itemsRoute);
        break;
      case AppConstants.newTransactionTabIndex:
        context.go(AppConstants.newTransactionRoute);
        break;
      case AppConstants.unpaidTransactionsTabIndex:
        context.go(AppConstants.unpaidTransactionsRoute);
        break;
      case AppConstants.transactionHistoryTabIndex:
        context.go(AppConstants.transactionHistoryRoute);
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: widget.child,
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: _calculateSelectedIndex(context),
        onTap: (index) => _onItemTapped(index, context),
        type: BottomNavigationBarType.fixed,
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.inventory_2_outlined),
            activeIcon: Icon(Icons.inventory_2),
            label: AppConstants.titleItems,
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.add_shopping_cart_outlined),
            activeIcon: Icon(Icons.add_shopping_cart),
            label: AppConstants.titleNewTransaction,
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.payment_outlined),
            activeIcon: Icon(Icons.payment),
            label: AppConstants.titleUnpaidTransactions,
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.history_outlined),
            activeIcon: Icon(Icons.history),
            label: AppConstants.titleTransactionHistory,
          ),
        ],
      ),
    );
  }
}
