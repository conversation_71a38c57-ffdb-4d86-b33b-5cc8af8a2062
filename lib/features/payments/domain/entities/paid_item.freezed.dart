// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'paid_item.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

PaidItemEntity _$PaidItemEntityFromJson(Map<String, dynamic> json) {
  return _PaidItemEntity.fromJson(json);
}

/// @nodoc
mixin _$PaidItemEntity {
  int get id => throw _privateConstructorUsedError;
  int get paymentId => throw _privateConstructorUsedError;
  int get transactionItemId => throw _privateConstructorUsedError;
  int get quantity => throw _privateConstructorUsedError;
  double get amount => throw _privateConstructorUsedError;
  DateTime get createdAt => throw _privateConstructorUsedError;

  /// Serializes this PaidItemEntity to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of PaidItemEntity
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $PaidItemEntityCopyWith<PaidItemEntity> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PaidItemEntityCopyWith<$Res> {
  factory $PaidItemEntityCopyWith(
    PaidItemEntity value,
    $Res Function(PaidItemEntity) then,
  ) = _$PaidItemEntityCopyWithImpl<$Res, PaidItemEntity>;
  @useResult
  $Res call({
    int id,
    int paymentId,
    int transactionItemId,
    int quantity,
    double amount,
    DateTime createdAt,
  });
}

/// @nodoc
class _$PaidItemEntityCopyWithImpl<$Res, $Val extends PaidItemEntity>
    implements $PaidItemEntityCopyWith<$Res> {
  _$PaidItemEntityCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PaidItemEntity
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? paymentId = null,
    Object? transactionItemId = null,
    Object? quantity = null,
    Object? amount = null,
    Object? createdAt = null,
  }) {
    return _then(
      _value.copyWith(
            id:
                null == id
                    ? _value.id
                    : id // ignore: cast_nullable_to_non_nullable
                        as int,
            paymentId:
                null == paymentId
                    ? _value.paymentId
                    : paymentId // ignore: cast_nullable_to_non_nullable
                        as int,
            transactionItemId:
                null == transactionItemId
                    ? _value.transactionItemId
                    : transactionItemId // ignore: cast_nullable_to_non_nullable
                        as int,
            quantity:
                null == quantity
                    ? _value.quantity
                    : quantity // ignore: cast_nullable_to_non_nullable
                        as int,
            amount:
                null == amount
                    ? _value.amount
                    : amount // ignore: cast_nullable_to_non_nullable
                        as double,
            createdAt:
                null == createdAt
                    ? _value.createdAt
                    : createdAt // ignore: cast_nullable_to_non_nullable
                        as DateTime,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$PaidItemEntityImplCopyWith<$Res>
    implements $PaidItemEntityCopyWith<$Res> {
  factory _$$PaidItemEntityImplCopyWith(
    _$PaidItemEntityImpl value,
    $Res Function(_$PaidItemEntityImpl) then,
  ) = __$$PaidItemEntityImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    int id,
    int paymentId,
    int transactionItemId,
    int quantity,
    double amount,
    DateTime createdAt,
  });
}

/// @nodoc
class __$$PaidItemEntityImplCopyWithImpl<$Res>
    extends _$PaidItemEntityCopyWithImpl<$Res, _$PaidItemEntityImpl>
    implements _$$PaidItemEntityImplCopyWith<$Res> {
  __$$PaidItemEntityImplCopyWithImpl(
    _$PaidItemEntityImpl _value,
    $Res Function(_$PaidItemEntityImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of PaidItemEntity
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? paymentId = null,
    Object? transactionItemId = null,
    Object? quantity = null,
    Object? amount = null,
    Object? createdAt = null,
  }) {
    return _then(
      _$PaidItemEntityImpl(
        id:
            null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                    as int,
        paymentId:
            null == paymentId
                ? _value.paymentId
                : paymentId // ignore: cast_nullable_to_non_nullable
                    as int,
        transactionItemId:
            null == transactionItemId
                ? _value.transactionItemId
                : transactionItemId // ignore: cast_nullable_to_non_nullable
                    as int,
        quantity:
            null == quantity
                ? _value.quantity
                : quantity // ignore: cast_nullable_to_non_nullable
                    as int,
        amount:
            null == amount
                ? _value.amount
                : amount // ignore: cast_nullable_to_non_nullable
                    as double,
        createdAt:
            null == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                    as DateTime,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$PaidItemEntityImpl implements _PaidItemEntity {
  const _$PaidItemEntityImpl({
    required this.id,
    required this.paymentId,
    required this.transactionItemId,
    required this.quantity,
    required this.amount,
    required this.createdAt,
  });

  factory _$PaidItemEntityImpl.fromJson(Map<String, dynamic> json) =>
      _$$PaidItemEntityImplFromJson(json);

  @override
  final int id;
  @override
  final int paymentId;
  @override
  final int transactionItemId;
  @override
  final int quantity;
  @override
  final double amount;
  @override
  final DateTime createdAt;

  @override
  String toString() {
    return 'PaidItemEntity(id: $id, paymentId: $paymentId, transactionItemId: $transactionItemId, quantity: $quantity, amount: $amount, createdAt: $createdAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PaidItemEntityImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.paymentId, paymentId) ||
                other.paymentId == paymentId) &&
            (identical(other.transactionItemId, transactionItemId) ||
                other.transactionItemId == transactionItemId) &&
            (identical(other.quantity, quantity) ||
                other.quantity == quantity) &&
            (identical(other.amount, amount) || other.amount == amount) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    paymentId,
    transactionItemId,
    quantity,
    amount,
    createdAt,
  );

  /// Create a copy of PaidItemEntity
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PaidItemEntityImplCopyWith<_$PaidItemEntityImpl> get copyWith =>
      __$$PaidItemEntityImplCopyWithImpl<_$PaidItemEntityImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$PaidItemEntityImplToJson(this);
  }
}

abstract class _PaidItemEntity implements PaidItemEntity {
  const factory _PaidItemEntity({
    required final int id,
    required final int paymentId,
    required final int transactionItemId,
    required final int quantity,
    required final double amount,
    required final DateTime createdAt,
  }) = _$PaidItemEntityImpl;

  factory _PaidItemEntity.fromJson(Map<String, dynamic> json) =
      _$PaidItemEntityImpl.fromJson;

  @override
  int get id;
  @override
  int get paymentId;
  @override
  int get transactionItemId;
  @override
  int get quantity;
  @override
  double get amount;
  @override
  DateTime get createdAt;

  /// Create a copy of PaidItemEntity
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PaidItemEntityImplCopyWith<_$PaidItemEntityImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
