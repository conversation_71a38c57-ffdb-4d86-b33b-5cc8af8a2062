import 'package:freezed_annotation/freezed_annotation.dart';

part 'paid_item.freezed.dart';
part 'paid_item.g.dart';

@freezed
class PaidItemEntity with _$PaidItemEntity {
  const factory PaidItemEntity({
    required int id,
    required int paymentId,
    required int transactionItemId,
    required int quantity,
    required double amount,
    required DateTime createdAt,
  }) = _PaidItemEntity;

  factory PaidItemEntity.fromJson(Map<String, dynamic> json) => _$PaidItemEntityFromJson(json);
}
