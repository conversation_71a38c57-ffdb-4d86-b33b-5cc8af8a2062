// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'transaction_with_payments.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

TransactionWithPaymentsEntity _$TransactionWithPaymentsEntityFromJson(
  Map<String, dynamic> json,
) {
  return _TransactionWithPaymentsEntity.fromJson(json);
}

/// @nodoc
mixin _$TransactionWithPaymentsEntity {
  TransactionEntity get transaction => throw _privateConstructorUsedError;
  List<PaymentEntity> get payments => throw _privateConstructorUsedError;

  /// Serializes this TransactionWithPaymentsEntity to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of TransactionWithPaymentsEntity
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $TransactionWithPaymentsEntityCopyWith<TransactionWithPaymentsEntity>
  get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TransactionWithPaymentsEntityCopyWith<$Res> {
  factory $TransactionWithPaymentsEntityCopyWith(
    TransactionWithPaymentsEntity value,
    $Res Function(TransactionWithPaymentsEntity) then,
  ) =
      _$TransactionWithPaymentsEntityCopyWithImpl<
        $Res,
        TransactionWithPaymentsEntity
      >;
  @useResult
  $Res call({TransactionEntity transaction, List<PaymentEntity> payments});

  $TransactionEntityCopyWith<$Res> get transaction;
}

/// @nodoc
class _$TransactionWithPaymentsEntityCopyWithImpl<
  $Res,
  $Val extends TransactionWithPaymentsEntity
>
    implements $TransactionWithPaymentsEntityCopyWith<$Res> {
  _$TransactionWithPaymentsEntityCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of TransactionWithPaymentsEntity
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? transaction = null, Object? payments = null}) {
    return _then(
      _value.copyWith(
            transaction:
                null == transaction
                    ? _value.transaction
                    : transaction // ignore: cast_nullable_to_non_nullable
                        as TransactionEntity,
            payments:
                null == payments
                    ? _value.payments
                    : payments // ignore: cast_nullable_to_non_nullable
                        as List<PaymentEntity>,
          )
          as $Val,
    );
  }

  /// Create a copy of TransactionWithPaymentsEntity
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $TransactionEntityCopyWith<$Res> get transaction {
    return $TransactionEntityCopyWith<$Res>(_value.transaction, (value) {
      return _then(_value.copyWith(transaction: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$TransactionWithPaymentsEntityImplCopyWith<$Res>
    implements $TransactionWithPaymentsEntityCopyWith<$Res> {
  factory _$$TransactionWithPaymentsEntityImplCopyWith(
    _$TransactionWithPaymentsEntityImpl value,
    $Res Function(_$TransactionWithPaymentsEntityImpl) then,
  ) = __$$TransactionWithPaymentsEntityImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({TransactionEntity transaction, List<PaymentEntity> payments});

  @override
  $TransactionEntityCopyWith<$Res> get transaction;
}

/// @nodoc
class __$$TransactionWithPaymentsEntityImplCopyWithImpl<$Res>
    extends
        _$TransactionWithPaymentsEntityCopyWithImpl<
          $Res,
          _$TransactionWithPaymentsEntityImpl
        >
    implements _$$TransactionWithPaymentsEntityImplCopyWith<$Res> {
  __$$TransactionWithPaymentsEntityImplCopyWithImpl(
    _$TransactionWithPaymentsEntityImpl _value,
    $Res Function(_$TransactionWithPaymentsEntityImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of TransactionWithPaymentsEntity
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? transaction = null, Object? payments = null}) {
    return _then(
      _$TransactionWithPaymentsEntityImpl(
        transaction:
            null == transaction
                ? _value.transaction
                : transaction // ignore: cast_nullable_to_non_nullable
                    as TransactionEntity,
        payments:
            null == payments
                ? _value._payments
                : payments // ignore: cast_nullable_to_non_nullable
                    as List<PaymentEntity>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$TransactionWithPaymentsEntityImpl
    implements _TransactionWithPaymentsEntity {
  const _$TransactionWithPaymentsEntityImpl({
    required this.transaction,
    required final List<PaymentEntity> payments,
  }) : _payments = payments;

  factory _$TransactionWithPaymentsEntityImpl.fromJson(
    Map<String, dynamic> json,
  ) => _$$TransactionWithPaymentsEntityImplFromJson(json);

  @override
  final TransactionEntity transaction;
  final List<PaymentEntity> _payments;
  @override
  List<PaymentEntity> get payments {
    if (_payments is EqualUnmodifiableListView) return _payments;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_payments);
  }

  @override
  String toString() {
    return 'TransactionWithPaymentsEntity(transaction: $transaction, payments: $payments)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TransactionWithPaymentsEntityImpl &&
            (identical(other.transaction, transaction) ||
                other.transaction == transaction) &&
            const DeepCollectionEquality().equals(other._payments, _payments));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    transaction,
    const DeepCollectionEquality().hash(_payments),
  );

  /// Create a copy of TransactionWithPaymentsEntity
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TransactionWithPaymentsEntityImplCopyWith<
    _$TransactionWithPaymentsEntityImpl
  >
  get copyWith => __$$TransactionWithPaymentsEntityImplCopyWithImpl<
    _$TransactionWithPaymentsEntityImpl
  >(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$TransactionWithPaymentsEntityImplToJson(this);
  }
}

abstract class _TransactionWithPaymentsEntity
    implements TransactionWithPaymentsEntity {
  const factory _TransactionWithPaymentsEntity({
    required final TransactionEntity transaction,
    required final List<PaymentEntity> payments,
  }) = _$TransactionWithPaymentsEntityImpl;

  factory _TransactionWithPaymentsEntity.fromJson(Map<String, dynamic> json) =
      _$TransactionWithPaymentsEntityImpl.fromJson;

  @override
  TransactionEntity get transaction;
  @override
  List<PaymentEntity> get payments;

  /// Create a copy of TransactionWithPaymentsEntity
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TransactionWithPaymentsEntityImplCopyWith<
    _$TransactionWithPaymentsEntityImpl
  >
  get copyWith => throw _privateConstructorUsedError;
}
