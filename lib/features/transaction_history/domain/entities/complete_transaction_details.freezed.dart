// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'complete_transaction_details.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

CompleteTransactionDetailsEntity _$CompleteTransactionDetailsEntityFromJson(
  Map<String, dynamic> json,
) {
  return _CompleteTransactionDetailsEntity.fromJson(json);
}

/// @nodoc
mixin _$CompleteTransactionDetailsEntity {
  TransactionEntity get transaction => throw _privateConstructorUsedError;
  List<TransactionItemWithDetailsEntity> get items =>
      throw _privateConstructorUsedError;
  List<PaymentEntity> get payments => throw _privateConstructorUsedError;

  /// Serializes this CompleteTransactionDetailsEntity to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CompleteTransactionDetailsEntity
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CompleteTransactionDetailsEntityCopyWith<CompleteTransactionDetailsEntity>
  get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CompleteTransactionDetailsEntityCopyWith<$Res> {
  factory $CompleteTransactionDetailsEntityCopyWith(
    CompleteTransactionDetailsEntity value,
    $Res Function(CompleteTransactionDetailsEntity) then,
  ) =
      _$CompleteTransactionDetailsEntityCopyWithImpl<
        $Res,
        CompleteTransactionDetailsEntity
      >;
  @useResult
  $Res call({
    TransactionEntity transaction,
    List<TransactionItemWithDetailsEntity> items,
    List<PaymentEntity> payments,
  });

  $TransactionEntityCopyWith<$Res> get transaction;
}

/// @nodoc
class _$CompleteTransactionDetailsEntityCopyWithImpl<
  $Res,
  $Val extends CompleteTransactionDetailsEntity
>
    implements $CompleteTransactionDetailsEntityCopyWith<$Res> {
  _$CompleteTransactionDetailsEntityCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CompleteTransactionDetailsEntity
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? transaction = null,
    Object? items = null,
    Object? payments = null,
  }) {
    return _then(
      _value.copyWith(
            transaction:
                null == transaction
                    ? _value.transaction
                    : transaction // ignore: cast_nullable_to_non_nullable
                        as TransactionEntity,
            items:
                null == items
                    ? _value.items
                    : items // ignore: cast_nullable_to_non_nullable
                        as List<TransactionItemWithDetailsEntity>,
            payments:
                null == payments
                    ? _value.payments
                    : payments // ignore: cast_nullable_to_non_nullable
                        as List<PaymentEntity>,
          )
          as $Val,
    );
  }

  /// Create a copy of CompleteTransactionDetailsEntity
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $TransactionEntityCopyWith<$Res> get transaction {
    return $TransactionEntityCopyWith<$Res>(_value.transaction, (value) {
      return _then(_value.copyWith(transaction: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$CompleteTransactionDetailsEntityImplCopyWith<$Res>
    implements $CompleteTransactionDetailsEntityCopyWith<$Res> {
  factory _$$CompleteTransactionDetailsEntityImplCopyWith(
    _$CompleteTransactionDetailsEntityImpl value,
    $Res Function(_$CompleteTransactionDetailsEntityImpl) then,
  ) = __$$CompleteTransactionDetailsEntityImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    TransactionEntity transaction,
    List<TransactionItemWithDetailsEntity> items,
    List<PaymentEntity> payments,
  });

  @override
  $TransactionEntityCopyWith<$Res> get transaction;
}

/// @nodoc
class __$$CompleteTransactionDetailsEntityImplCopyWithImpl<$Res>
    extends
        _$CompleteTransactionDetailsEntityCopyWithImpl<
          $Res,
          _$CompleteTransactionDetailsEntityImpl
        >
    implements _$$CompleteTransactionDetailsEntityImplCopyWith<$Res> {
  __$$CompleteTransactionDetailsEntityImplCopyWithImpl(
    _$CompleteTransactionDetailsEntityImpl _value,
    $Res Function(_$CompleteTransactionDetailsEntityImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of CompleteTransactionDetailsEntity
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? transaction = null,
    Object? items = null,
    Object? payments = null,
  }) {
    return _then(
      _$CompleteTransactionDetailsEntityImpl(
        transaction:
            null == transaction
                ? _value.transaction
                : transaction // ignore: cast_nullable_to_non_nullable
                    as TransactionEntity,
        items:
            null == items
                ? _value._items
                : items // ignore: cast_nullable_to_non_nullable
                    as List<TransactionItemWithDetailsEntity>,
        payments:
            null == payments
                ? _value._payments
                : payments // ignore: cast_nullable_to_non_nullable
                    as List<PaymentEntity>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$CompleteTransactionDetailsEntityImpl
    implements _CompleteTransactionDetailsEntity {
  const _$CompleteTransactionDetailsEntityImpl({
    required this.transaction,
    required final List<TransactionItemWithDetailsEntity> items,
    required final List<PaymentEntity> payments,
  }) : _items = items,
       _payments = payments;

  factory _$CompleteTransactionDetailsEntityImpl.fromJson(
    Map<String, dynamic> json,
  ) => _$$CompleteTransactionDetailsEntityImplFromJson(json);

  @override
  final TransactionEntity transaction;
  final List<TransactionItemWithDetailsEntity> _items;
  @override
  List<TransactionItemWithDetailsEntity> get items {
    if (_items is EqualUnmodifiableListView) return _items;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_items);
  }

  final List<PaymentEntity> _payments;
  @override
  List<PaymentEntity> get payments {
    if (_payments is EqualUnmodifiableListView) return _payments;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_payments);
  }

  @override
  String toString() {
    return 'CompleteTransactionDetailsEntity(transaction: $transaction, items: $items, payments: $payments)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CompleteTransactionDetailsEntityImpl &&
            (identical(other.transaction, transaction) ||
                other.transaction == transaction) &&
            const DeepCollectionEquality().equals(other._items, _items) &&
            const DeepCollectionEquality().equals(other._payments, _payments));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    transaction,
    const DeepCollectionEquality().hash(_items),
    const DeepCollectionEquality().hash(_payments),
  );

  /// Create a copy of CompleteTransactionDetailsEntity
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CompleteTransactionDetailsEntityImplCopyWith<
    _$CompleteTransactionDetailsEntityImpl
  >
  get copyWith => __$$CompleteTransactionDetailsEntityImplCopyWithImpl<
    _$CompleteTransactionDetailsEntityImpl
  >(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CompleteTransactionDetailsEntityImplToJson(this);
  }
}

abstract class _CompleteTransactionDetailsEntity
    implements CompleteTransactionDetailsEntity {
  const factory _CompleteTransactionDetailsEntity({
    required final TransactionEntity transaction,
    required final List<TransactionItemWithDetailsEntity> items,
    required final List<PaymentEntity> payments,
  }) = _$CompleteTransactionDetailsEntityImpl;

  factory _CompleteTransactionDetailsEntity.fromJson(
    Map<String, dynamic> json,
  ) = _$CompleteTransactionDetailsEntityImpl.fromJson;

  @override
  TransactionEntity get transaction;
  @override
  List<TransactionItemWithDetailsEntity> get items;
  @override
  List<PaymentEntity> get payments;

  /// Create a copy of CompleteTransactionDetailsEntity
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CompleteTransactionDetailsEntityImplCopyWith<
    _$CompleteTransactionDetailsEntityImpl
  >
  get copyWith => throw _privateConstructorUsedError;
}
