import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/datasources/local/database/database.dart';
import '../../../transactions/data/repositories/transaction_repository_impl.dart';
import '../../../transactions/domain/repositories/transaction_repository.dart';
import '../../domain/usecases/transaction_history_usecases.dart';

// Database provider
final databaseProvider = Provider<AppDatabase>((ref) {
  return AppDatabase();
});

// Repository provider
final transactionRepositoryProvider = Provider<TransactionRepository>((ref) {
  final database = ref.watch(databaseProvider);
  return TransactionRepositoryImpl(database);
});

// Use case providers
final getPaidTransactionsUseCaseProvider = Provider<GetPaidTransactionsUseCase>((ref) {
  return GetPaidTransactionsUseCase(ref.watch(transactionRepositoryProvider));
});

final getTransactionWithPaymentsUseCaseProvider = Provider<GetTransactionWithPaymentsUseCase>((ref) {
  return GetTransactionWithPaymentsUseCase(ref.watch(transactionRepositoryProvider));
});

final getCompleteTransactionDetailsUseCaseProvider = Provider<GetCompleteTransactionDetailsUseCase>((ref) {
  return GetCompleteTransactionDetailsUseCase(ref.watch(transactionRepositoryProvider));
});

final getPaymentPaidItemsUseCaseProvider = Provider<GetPaymentPaidItemsUseCase>((ref) {
  return GetPaymentPaidItemsUseCase(ref.watch(transactionRepositoryProvider));
});
