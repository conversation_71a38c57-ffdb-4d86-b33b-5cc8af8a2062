import '../entities/transaction.dart';
import '../entities/transaction_item_with_details.dart';
import '../repositories/transaction_repository.dart';
import '../../../payments/domain/entities/payment.dart';
import '../../../payments/domain/entities/paid_item.dart';

/// Use case for handling atomic payment distribution across multiple items
/// Ensures all payment operations are completed successfully or rolled back
class AtomicPaymentDistributionUseCase {
  final TransactionRepository _repository;

  AtomicPaymentDistributionUseCase(this._repository);

  /// Distributes a payment across selected items atomically
  /// Returns the payment ID if successful, throws exception if failed
  Future<int> call({
    required List<PaymentDistributionItem> distributionItems,
    required DateTime paymentDate,
  }) async {
    if (distributionItems.isEmpty) {
      throw Exception('No items selected for payment distribution');
    }

    // Group items by transaction
    final itemsByTransaction = <int, List<PaymentDistributionItem>>{};
    for (final item in distributionItems) {
      final transactionId = item.transaction.id;
      itemsByTransaction.putIfAbsent(transactionId, () => []).add(item);
    }

    // Process each transaction
    for (final entry in itemsByTransaction.entries) {
      final transactionId = entry.key;
      final itemsForTransaction = entry.value;

      // Calculate total payment for this transaction
      final totalPayment = itemsForTransaction.fold(
        0.0,
        (sum, item) => sum + item.amountToPay,
      );

      // Create payment entity
      final payment = PaymentEntity(
        id: 0,
        transactionId: transactionId,
        amount: totalPayment,
        date: paymentDate,
        createdAt: DateTime.now(),
      );

      // Create paid items
      final paidItems = itemsForTransaction.map((item) {
        final transactionItem = item.itemWithDetails.transactionItem;
        final pricePerUnit = transactionItem.priceAtPurchase;
        final amountBeingPaid = item.amountToPay;

        // Calculate the correct quantity being paid for this item
        int quantityBeingPaid;
        if (amountBeingPaid >= transactionItem.remainingAmount) {
          // Full payment - use the full quantity of the transaction item
          quantityBeingPaid = transactionItem.quantity;
        } else {
          // Partial payment - calculate equivalent quantity
          quantityBeingPaid = (amountBeingPaid / pricePerUnit).round().clamp(1, transactionItem.quantity);
        }

        return PaidItemEntity(
          id: 0,
          paymentId: 0, // Will be set by database
          transactionItemId: transactionItem.id,
          quantity: quantityBeingPaid,
          amount: amountBeingPaid,
          createdAt: DateTime.now(),
        );
      }).toList();

      // Calculate new transaction remaining amount
      final currentTransaction = itemsForTransaction.first.transaction;
      final newRemainingAmount = currentTransaction.remainingAmount - totalPayment;

      // Determine new status
      final newStatus = newRemainingAmount <= 0
          ? 'Paid_'
          : 'Partially Paid';

      // Create updated transaction
      final updatedTransaction = currentTransaction.copyWith(
        remainingAmount: newRemainingAmount > 0 ? newRemainingAmount : 0,
        status: newStatus,
        updatedAt: DateTime.now(),
      );

      // Execute atomic payment operation
      final paymentId = await _repository.addPaymentAndUpdateTransaction(
        payment,
        updatedTransaction,
        paidItems,
      );

      // Update individual item remaining amounts
      for (final item in itemsForTransaction) {
        final newItemRemainingAmount = item.itemWithDetails.transactionItem.remainingAmount - item.amountToPay;
        await _repository.updateTransactionItemRemainingAmount(
          item.itemWithDetails.transactionItem.id,
          newItemRemainingAmount > 0 ? newItemRemainingAmount : 0,
        );
      }

      // Recalculate transaction totals to ensure consistency
      await _repository.recalculateTransactionRemainingAmount(transactionId);
    }

    return distributionItems.first.transaction.id; // Return first transaction ID as reference
  }
}

/// Data class for payment distribution items
class PaymentDistributionItem {
  final TransactionEntity transaction;
  final TransactionItemWithDetailsEntity itemWithDetails;
  final double amountToPay;

  PaymentDistributionItem({
    required this.transaction,
    required this.itemWithDetails,
    required this.amountToPay,
  });
}
