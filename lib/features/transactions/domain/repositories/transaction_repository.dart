import '../entities/transaction.dart';
import '../entities/transaction_with_items.dart';
import '../../../transaction_history/domain/entities/transaction_with_payments.dart';
import '../../../transaction_history/domain/entities/complete_transaction_details.dart';
import '../entities/transaction_item.dart';
import '../entities/transaction_item_with_details.dart';
import '../../../payments/domain/entities/payment.dart';
import '../../../payments/domain/entities/paid_item.dart';

abstract class TransactionRepository {
  Future<List<TransactionEntity>> getAllTransactions();
  Future<List<TransactionEntity>> getUnpaidTransactions();
  Future<List<TransactionEntity>> getPaidTransactions();
  Future<TransactionEntity> getTransactionById(int id);
  Future<TransactionWithItemsEntity> getTransactionWithItems(int transactionId);
  Future<TransactionWithPaymentsEntity> getTransactionWithPayments(int transactionId);
  Future<CompleteTransactionDetailsEntity> getCompleteTransactionDetails(int transactionId);
  Future<int> createTransactionWithItems(
    TransactionEntity transaction,
    List<TransactionItemEntity> items,
  );
  Future<int> addPaymentAndUpdateTransaction(
    PaymentEntity payment,
    TransactionEntity updatedTransaction,
    List<PaidItemEntity> paidItems,
  );
  Future<List<PaidItemEntity>> getPaymentPaidItems(int paymentId);
  Future<List<PaidItemEntity>> getPaidItemsForTransactionItem(int transactionItemId);

  // New methods for item-level payment tracking
  Future<List<TransactionItemWithDetailsEntity>> getUnpaidTransactionItems();
  Future<void> updateTransactionItemRemainingAmount(int transactionItemId, double newRemainingAmount);
  Future<void> recalculateTransactionRemainingAmount(int transactionId);

  // Transaction CRUD operations
  Future<bool> updateTransaction(TransactionEntity transaction);
  Future<int> deleteTransaction(int transactionId);
}
