import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../../../core/constants/app_constants.dart';
import '../../../../../core/utils/formatters.dart';
import '../../../../items/domain/entities/item.dart';

class TransactionItemCard extends StatefulWidget {
  final ItemEntity item;
  final int quantity;
  final Function(int) onQuantityChanged;
  final VoidCallback onRemove;

  const TransactionItemCard({
    super.key,
    required this.item,
    required this.quantity,
    required this.onQuantityChanged,
    required this.onRemove,
  });

  @override
  State<TransactionItemCard> createState() => _TransactionItemCardState();
}

class _TransactionItemCardState extends State<TransactionItemCard> {
  late TextEditingController _quantityController;

  @override
  void initState() {
    super.initState();
    _quantityController = TextEditingController(text: widget.quantity.toString());
  }

  @override
  void didUpdateWidget(TransactionItemCard oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.quantity != widget.quantity) {
      _quantityController.text = widget.quantity.toString();
    }
  }

  @override
  void dispose() {
    _quantityController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final totalPrice = widget.item.price * widget.quantity;

    return Card(
      margin: const EdgeInsets.only(bottom: AppConstants.smallPadding),
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    widget.item.name,
                    style: Theme.of(context).textTheme.titleMedium,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.delete_outline),
                  color: Theme.of(context).colorScheme.error,
                  onPressed: widget.onRemove,
                  tooltip: 'Remove Item',
                ),
              ],
            ),
            const SizedBox(height: AppConstants.smallPadding),
            Row(
              children: [
                Text(
                  '${AppConstants.labelItemPrice}: ',
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
                Text(
                  Formatters.formatCurrency(widget.item.price),
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
              ],
            ),
            const SizedBox(height: AppConstants.smallPadding),
            Row(
              children: [
                Text(
                  '${AppConstants.labelQuantity}: ',
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
                SizedBox(
                  width: 60,
                  child: TextFormField(
                    controller: _quantityController,
                    keyboardType: TextInputType.number,
                    inputFormatters: [
                      FilteringTextInputFormatter.digitsOnly,
                    ],
                    decoration: const InputDecoration(
                      contentPadding: EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 8,
                      ),
                    ),
                    onChanged: (value) {
                      final quantity = int.tryParse(value) ?? 0;
                      if (quantity >= 1) {
                        widget.onQuantityChanged(quantity);
                      }
                    },
                  ),
                ),
                const Spacer(),
                Text(
                  Formatters.formatCurrency(totalPrice),
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: Theme.of(context).colorScheme.primary,
                      ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
