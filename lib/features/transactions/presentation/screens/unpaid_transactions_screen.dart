import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/utils/formatters.dart';
import '../providers/unpaid_transactions_provider.dart';
import '../../../../core/widgets/empty_state.dart';
import '../../../../core/widgets/error_display.dart';
import '../../../../core/widgets/loading_indicator.dart';
import 'widgets/unpaid_transaction_card.dart';
import 'widgets/global_payment_bottom_sheet.dart';

class UnpaidTransactionsScreen extends ConsumerWidget {
  const UnpaidTransactionsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final transactionsState = ref.watch(unpaidTransactionsProvider);

    // Transactions are now sorted by date (newest first) in the provider

    return Scaffold(
      appBar: AppBar(
        title: const Text(AppConstants.titleUnpaidTransactions),
      ),
      body: RefreshIndicator(
        onRefresh: () => ref.read(unpaidTransactionsProvider.notifier).refreshTransactions(),
        child: transactionsState.when(
          data: (transactionGroups) {
            if (transactionGroups.isEmpty) {
              return const EmptyState(
                message: AppConstants.emptyUnpaidTransactions,
                icon: Icons.payment_outlined,
              );
            }

            // Calculate grand total
            final grandTotal = ref.read(unpaidTransactionsProvider.notifier).getGrandTotal();

            return Column(
              children: [
                // Grand Total Card
                Padding(
                  padding: const EdgeInsets.all(AppConstants.defaultPadding),
                  child: Card(
                    color: Theme.of(context).colorScheme.primary.withAlpha(26),
                    child: Padding(
                      padding: const EdgeInsets.all(AppConstants.defaultPadding),
                      child: Column(
                        children: [
                          Text(
                            AppConstants.labelGrandTotal,
                            style: Theme.of(context).textTheme.titleMedium,
                          ),
                          const SizedBox(height: AppConstants.smallPadding),
                          Text(
                            Formatters.formatCurrency(grandTotal),
                            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                                  color: Theme.of(context).colorScheme.primary,
                                  fontWeight: FontWeight.bold,
                                ),
                          ),
                          const SizedBox(height: AppConstants.defaultPadding),
                          SizedBox(
                            width: double.infinity,
                            child: ElevatedButton.icon(
                              onPressed: grandTotal > 0
                                  ? () => _showGlobalPaymentBottomSheet(context, ref, grandTotal)
                                  : null,
                              icon: const Icon(Icons.payment),
                              label: const Text(AppConstants.buttonPayAll),
                              style: ElevatedButton.styleFrom(
                                padding: const EdgeInsets.symmetric(vertical: 12),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),

                // Transactions List - Grouped by Date
                Expanded(
                  child: _buildGroupedTransactionsList(transactionGroups),
                ),
              ],
            );
          },
          loading: () => const LoadingIndicator(),
          error: (error, stackTrace) => ErrorDisplay(
            message: 'Error loading unpaid transactions: $error',
            onRetry: () => ref.read(unpaidTransactionsProvider.notifier).refreshTransactions(),
          ),
        ),
      ),
    );
  }

  Widget _buildGroupedTransactionsList(List<UnpaidTransactionGroup> transactionGroups) {
    // Group transaction groups by date
    final Map<DateTime, List<UnpaidTransactionGroup>> groupedTransactions = {};

    for (final group in transactionGroups) {
      final dateKey = DateTime(group.transaction.date.year, group.transaction.date.month, group.transaction.date.day);
      groupedTransactions.putIfAbsent(dateKey, () => []).add(group);
    }

    // Sort dates in descending order (most recent first)
    final sortedDates = groupedTransactions.keys.toList()
      ..sort((a, b) => b.compareTo(a));

    return ListView.builder(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      itemCount: sortedDates.length,
      itemBuilder: (context, index) {
        final date = sortedDates[index];
        final groupsForDate = groupedTransactions[date]!;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Date header
            Padding(
              padding: EdgeInsets.only(
                bottom: AppConstants.smallPadding,
                top: index == 0 ? 0 : AppConstants.defaultPadding,
              ),
              child: Text(
                Formatters.formatDate(date),
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey,
                ),
              ),
            ),

            // Transaction groups for this date
            ...groupsForDate.map((group) => UnpaidTransactionCard(
              transaction: group.transaction,
              unpaidItems: group.unpaidItems,
              totalUnpaidAmount: group.totalUnpaidAmount,
            )),
          ],
        );
      },
    );
  }

  void _showGlobalPaymentBottomSheet(BuildContext context, WidgetRef ref, double grandTotal) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(AppConstants.bottomSheetBorderRadius),
        ),
      ),
      builder: (context) => Padding(
        padding: EdgeInsets.only(
          bottom: MediaQuery.of(context).viewInsets.bottom,
        ),
        child: GlobalPaymentBottomSheet(grandTotal: grandTotal),
      ),
    );
  }
}
