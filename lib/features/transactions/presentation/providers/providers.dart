import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/datasources/local/database/database.dart';
import '../../data/repositories/transaction_repository_impl.dart';
import '../../domain/repositories/transaction_repository.dart';
import '../../domain/usecases/transaction_usecases.dart';
import '../../domain/usecases/atomic_payment_distribution_usecase.dart';

// Database provider
final databaseProvider = Provider<AppDatabase>((ref) {
  return AppDatabase();
});

// Repository providers
final transactionRepositoryProvider = Provider<TransactionRepository>((ref) {
  final database = ref.watch(databaseProvider);
  return TransactionRepositoryImpl(database);
});

// Transaction use case providers
final getAllTransactionsUseCaseProvider = Provider<GetAllTransactionsUseCase>((ref) {
  final repository = ref.watch(transactionRepositoryProvider);
  return GetAllTransactionsUseCase(repository);
});

final getUnpaidTransactionsUseCaseProvider = Provider<GetUnpaidTransactionsUseCase>((ref) {
  final repository = ref.watch(transactionRepositoryProvider);
  return GetUnpaidTransactionsUseCase(repository);
});

final getPaidTransactionsUseCaseProvider = Provider<GetPaidTransactionsUseCase>((ref) {
  final repository = ref.watch(transactionRepositoryProvider);
  return GetPaidTransactionsUseCase(repository);
});

final getTransactionByIdUseCaseProvider = Provider<GetTransactionByIdUseCase>((ref) {
  final repository = ref.watch(transactionRepositoryProvider);
  return GetTransactionByIdUseCase(repository);
});

final createTransactionWithItemsUseCaseProvider = Provider<CreateTransactionWithItemsUseCase>((ref) {
  final repository = ref.watch(transactionRepositoryProvider);
  return CreateTransactionWithItemsUseCase(repository);
});

final addPaymentAndUpdateTransactionUseCaseProvider = Provider<AddPaymentAndUpdateTransactionUseCase>((ref) {
  final repository = ref.watch(transactionRepositoryProvider);
  return AddPaymentAndUpdateTransactionUseCase(repository);
});

final getUnpaidTransactionItemsUseCaseProvider = Provider<GetUnpaidTransactionItemsUseCase>((ref) {
  final repository = ref.watch(transactionRepositoryProvider);
  return GetUnpaidTransactionItemsUseCase(repository);
});

final updateTransactionItemRemainingAmountUseCaseProvider = Provider<UpdateTransactionItemRemainingAmountUseCase>((ref) {
  final repository = ref.watch(transactionRepositoryProvider);
  return UpdateTransactionItemRemainingAmountUseCase(repository);
});

final recalculateTransactionRemainingAmountUseCaseProvider = Provider<RecalculateTransactionRemainingAmountUseCase>((ref) {
  final repository = ref.watch(transactionRepositoryProvider);
  return RecalculateTransactionRemainingAmountUseCase(repository);
});

final updateTransactionUseCaseProvider = Provider<UpdateTransactionUseCase>((ref) {
  final repository = ref.watch(transactionRepositoryProvider);
  return UpdateTransactionUseCase(repository);
});

final deleteTransactionUseCaseProvider = Provider<DeleteTransactionUseCase>((ref) {
  final repository = ref.watch(transactionRepositoryProvider);
  return DeleteTransactionUseCase(repository);
});

final getTransactionWithItemsUseCaseProvider = Provider<GetTransactionWithItemsUseCase>((ref) {
  final repository = ref.watch(transactionRepositoryProvider);
  return GetTransactionWithItemsUseCase(repository);
});

final getTransactionWithPaymentsUseCaseProvider = Provider<GetTransactionWithPaymentsUseCase>((ref) {
  final repository = ref.watch(transactionRepositoryProvider);
  return GetTransactionWithPaymentsUseCase(repository);
});

final getCompleteTransactionDetailsUseCaseProvider = Provider<GetCompleteTransactionDetailsUseCase>((ref) {
  final repository = ref.watch(transactionRepositoryProvider);
  return GetCompleteTransactionDetailsUseCase(repository);
});

final getPaymentPaidItemsUseCaseProvider = Provider<GetPaymentPaidItemsUseCase>((ref) {
  final repository = ref.watch(transactionRepositoryProvider);
  return GetPaymentPaidItemsUseCase(repository);
});

final getPaidItemsForTransactionItemUseCaseProvider = Provider<GetPaidItemsForTransactionItemUseCase>((ref) {
  final repository = ref.watch(transactionRepositoryProvider);
  return GetPaidItemsForTransactionItemUseCase(repository);
});

final atomicPaymentDistributionUseCaseProvider = Provider<AtomicPaymentDistributionUseCase>((ref) {
  final repository = ref.watch(transactionRepositoryProvider);
  return AtomicPaymentDistributionUseCase(repository);
});
