import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../payments/domain/entities/payment.dart';
import '../../../payments/domain/entities/paid_item.dart';
import '../../domain/entities/transaction.dart';
import '../../domain/entities/transaction_with_items.dart';
import '../../domain/entities/transaction_item_with_details.dart';

import '../../domain/usecases/transaction_usecases.dart';
import 'providers.dart';
import '../../../transaction_history/presentation/providers/transaction_history_provider.dart';

// Helper class to track unpaid items across transactions
class _UnpaidItemInfo {
  final TransactionEntity transaction;
  final TransactionItemWithDetailsEntity itemWithDetails;
  final double remainingAmount;
  final double amountToPay; // Separate field for amount being paid

  _UnpaidItemInfo({
    required this.transaction,
    required this.itemWithDetails,
    required this.remainingAmount,
    double? amountToPay,
  }) : amountToPay = amountToPay ?? remainingAmount;
}

// Helper class to group unpaid items by transaction
class UnpaidTransactionGroup {
  final TransactionEntity transaction;
  final List<TransactionItemWithDetailsEntity> unpaidItems;
  final double totalUnpaidAmount;

  UnpaidTransactionGroup({
    required this.transaction,
    required this.unpaidItems,
    required this.totalUnpaidAmount,
  });
}

// Unpaid transactions notifier - now works with item-level data
class UnpaidTransactionsNotifier extends StateNotifier<AsyncValue<List<UnpaidTransactionGroup>>> {
  final GetUnpaidTransactionItemsUseCase _getUnpaidTransactionItemsUseCase;
  final GetTransactionWithItemsUseCase _getTransactionWithItemsUseCase;
  final AddPaymentAndUpdateTransactionUseCase _addPaymentAndUpdateTransactionUseCase;
  final Ref _ref;

  UnpaidTransactionsNotifier({
    required GetUnpaidTransactionItemsUseCase getUnpaidTransactionItemsUseCase,
    required GetTransactionWithItemsUseCase getTransactionWithItemsUseCase,
    required AddPaymentAndUpdateTransactionUseCase addPaymentAndUpdateTransactionUseCase,
    required Ref ref,
  })  : _getUnpaidTransactionItemsUseCase = getUnpaidTransactionItemsUseCase,
        _getTransactionWithItemsUseCase = getTransactionWithItemsUseCase,
        _addPaymentAndUpdateTransactionUseCase = addPaymentAndUpdateTransactionUseCase,
        _ref = ref,
        super(const AsyncValue.loading()) {
    refreshTransactions();
  }

  // Calculate the grand total of all unpaid items
  double getGrandTotal() {
    if (state is! AsyncData) {
      return 0.0;
    }

    final transactionGroups = (state as AsyncData<List<UnpaidTransactionGroup>>).value;
    return transactionGroups.fold(0.0, (sum, group) => sum + group.totalUnpaidAmount);
  }

  Future<void> refreshTransactions() async {
    state = const AsyncValue.loading();
    try {
      // Get all unpaid transaction items
      final unpaidItems = await _getUnpaidTransactionItemsUseCase();

      // Group items by transaction
      final Map<int, List<TransactionItemWithDetailsEntity>> itemsByTransaction = {};
      final Map<int, TransactionEntity> transactionMap = {};

      for (final item in unpaidItems) {
        final transactionId = item.transactionItem.transactionId;
        itemsByTransaction.putIfAbsent(transactionId, () => []).add(item);

        // We need to get the transaction entity for each unique transaction
        if (!transactionMap.containsKey(transactionId)) {
          // We'll fetch this in the next step
        }
      }

      // Fetch transaction entities for all unique transaction IDs
      final List<UnpaidTransactionGroup> transactionGroups = [];
      for (final transactionId in itemsByTransaction.keys) {
        try {
          final transactionWithItems = await _getTransactionWithItemsUseCase(transactionId);
          final transaction = transactionWithItems.transaction;
          final unpaidItemsForTransaction = itemsByTransaction[transactionId]!;

          // Calculate total unpaid amount for this transaction
          final totalUnpaidAmount = unpaidItemsForTransaction.fold(
            0.0,
            (sum, item) => sum + item.transactionItem.remainingAmount,
          );

          transactionGroups.add(UnpaidTransactionGroup(
            transaction: transaction,
            unpaidItems: unpaidItemsForTransaction,
            totalUnpaidAmount: totalUnpaidAmount,
          ));
        } catch (e) {
          debugPrint('Error fetching transaction $transactionId: $e');
        }
      }

      // Sort transaction groups by date (newest first)
      transactionGroups.sort((a, b) => b.transaction.date.compareTo(a.transaction.date));

      state = AsyncValue.data(transactionGroups);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  Future<TransactionWithItemsEntity> getTransactionWithItems(int transactionId) async {
    return await _getTransactionWithItemsUseCase(transactionId);
  }

  Future<bool> addPayment(int transactionId, double amount) async {
    try {
      // Get the current transaction group
      final transactionGroup = state.value?.firstWhere(
        (group) => group.transaction.id == transactionId,
        orElse: () => throw Exception('Transaction not found'),
      );

      if (transactionGroup == null) {
        return false;
      }

      final transaction = transactionGroup.transaction;

      // Get the transaction with items
      final transactionWithItems = await _getTransactionWithItemsUseCase(transactionId);

      // Calculate the new remaining amount
      final newRemainingAmount = transaction.remainingAmount - amount;

      // Determine the new status
      final newStatus = newRemainingAmount <= 0
          ? AppConstants.statusPaid
          : AppConstants.statusPartiallyPaid;

      // Create the payment entity
      final payment = PaymentEntity(
        id: 0, // Will be ignored by the database
        transactionId: transactionId,
        amount: amount,
        date: DateTime.now(),
        createdAt: DateTime.now(),
      );

      // Create the updated transaction entity
      final updatedTransaction = transaction.copyWith(
        remainingAmount: newRemainingAmount > 0 ? newRemainingAmount : 0,
        status: newStatus,
        updatedAt: DateTime.now(),
      );

      // Create paid items entities
      final paidItems = <PaidItemEntity>[];

      if (newStatus == AppConstants.statusPaid) {
        // If fully paid, add all items with their remaining amounts
        for (final item in transactionWithItems.items) {
          // For fully paid transactions, use the remaining amount for each item
          final remainingAmount = item.transactionItem.remainingAmount;
          if (remainingAmount > 0) {
            paidItems.add(
              PaidItemEntity(
                id: 0, // Will be ignored by the database
                paymentId: 0, // Will be set by the database
                transactionItemId: item.transactionItem.id,
                quantity: item.transactionItem.quantity,
                amount: remainingAmount, // Use remaining amount, not full original amount
                createdAt: DateTime.now(),
              ),
            );
          }
        }
      } else {
        // If partially paid, distribute payment proportionally across items with remaining amounts
        final totalRemainingAmount = transactionWithItems.items
            .fold(0.0, (sum, item) => sum + item.transactionItem.remainingAmount);

        if (totalRemainingAmount > 0) {
          for (final item in transactionWithItems.items) {
            final itemRemainingAmount = item.transactionItem.remainingAmount;
            if (itemRemainingAmount > 0) {
              // Calculate proportional payment for this item
              final itemPaymentRatio = itemRemainingAmount / totalRemainingAmount;
              final paidAmount = amount * itemPaymentRatio;

              // Calculate equivalent quantity for the amount being paid
              final pricePerUnit = item.transactionItem.priceAtPurchase;
              final quantityBeingPaid = (paidAmount / pricePerUnit).round().clamp(1, item.transactionItem.quantity);

              paidItems.add(
                PaidItemEntity(
                  id: 0, // Will be ignored by the database
                  paymentId: 0, // Will be set by the database
                  transactionItemId: item.transactionItem.id,
                  quantity: quantityBeingPaid,
                  amount: paidAmount,
                  createdAt: DateTime.now(),
                ),
              );
            }
          }
        }
      }

      // Add the payment and update the transaction
      await _addPaymentAndUpdateTransactionUseCase(payment, updatedTransaction, paidItems);

      // Refresh the transactions list
      refreshTransactions();

      // Always refresh the transaction history when any payment is made
      // This ensures partial payments also appear in the history
      _ref.read(baseTransactionHistoryProvider.notifier).refreshTransactions();

      return true;
    } catch (e) {
      debugPrint('Error in addPayment: $e');
      return false;
    }
  }

  // Add a global payment that is distributed across individual items
  Future<bool> addGlobalPayment(double amount, DateTime paymentDate) async {
    try {
      if (state is! AsyncData || amount <= 0) {
        return false;
      }

      // Get all unpaid transaction groups
      final transactionGroups = List<UnpaidTransactionGroup>.from((state as AsyncData<List<UnpaidTransactionGroup>>).value);

      // If there are no transaction groups, return false
      if (transactionGroups.isEmpty) {
        return false;
      }

      // Collect all unpaid items across all transactions
      final allUnpaidItems = <_UnpaidItemInfo>[];

      for (final group in transactionGroups) {
        for (final item in group.unpaidItems) {
          // Only include items that have remaining amount > 0
          if (item.transactionItem.remainingAmount > 0) {
            allUnpaidItems.add(_UnpaidItemInfo(
              transaction: group.transaction,
              itemWithDetails: item,
              remainingAmount: item.transactionItem.remainingAmount,
            ));
          }
        }
      }

      // Sort items by remaining amount (ascending) to prioritize full payments
      allUnpaidItems.sort((a, b) => a.remainingAmount.compareTo(b.remainingAmount));

      // Select items that total the payment amount using a greedy algorithm
      final selectedItems = _selectItemsForPayment(allUnpaidItems, amount);

      if (selectedItems.isEmpty) {
        return false;
      }

      // Group selected items by transaction for processing
      final itemsByTransaction = <int, List<_UnpaidItemInfo>>{};
      for (final item in selectedItems) {
        final transactionId = item.transaction.id;
        itemsByTransaction.putIfAbsent(transactionId, () => []).add(item);
      }

      bool atLeastOnePaymentMade = false;

      // Process each transaction that has selected items
      for (final entry in itemsByTransaction.entries) {
        final transactionId = entry.key;
        final selectedItemsForTransaction = entry.value;

        // Get the original transaction from the transaction groups
        final transactionGroup = transactionGroups.firstWhere((group) => group.transaction.id == transactionId);
        final transaction = transactionGroup.transaction;

        // Calculate total amount being paid for this transaction
        final totalPaidForTransaction = selectedItemsForTransaction
            .fold(0.0, (sum, item) => sum + item.amountToPay);

        // Create payment entity
        final payment = PaymentEntity(
          id: 0,
          transactionId: transactionId,
          amount: totalPaidForTransaction,
          date: paymentDate,
          createdAt: DateTime.now(),
        );

        // Create paid items entities for selected items only
        final paidItems = <PaidItemEntity>[];
        for (final selectedItem in selectedItemsForTransaction) {
          // Calculate the correct quantity being paid for this item
          final transactionItem = selectedItem.itemWithDetails.transactionItem;
          final pricePerUnit = transactionItem.priceAtPurchase;
          final amountBeingPaid = selectedItem.amountToPay;

          // For partial payments, calculate the equivalent quantity being paid
          // For full payments, use the remaining quantity
          int quantityBeingPaid;
          if (amountBeingPaid >= selectedItem.remainingAmount) {
            // Full payment - use the full quantity of the transaction item
            quantityBeingPaid = transactionItem.quantity;
          } else {
            // Partial payment - calculate equivalent quantity
            // Note: This represents the "equivalent" quantity for the amount being paid
            // For display purposes in transaction history
            quantityBeingPaid = (amountBeingPaid / pricePerUnit).round().clamp(1, transactionItem.quantity);
          }

          paidItems.add(
            PaidItemEntity(
              id: 0,
              paymentId: 0, // Will be set by the database
              transactionItemId: transactionItem.id,
              quantity: quantityBeingPaid,
              amount: amountBeingPaid, // Use the correct amount being paid
              createdAt: DateTime.now(),
            ),
          );
        }

        // Calculate new remaining amount for the transaction
        final newRemainingAmount = transaction.remainingAmount - totalPaidForTransaction;

        // Determine new status
        final newStatus = newRemainingAmount <= 0
            ? AppConstants.statusPaid
            : AppConstants.statusPartiallyPaid;

        // Create updated transaction
        final updatedTransaction = transaction.copyWith(
          remainingAmount: newRemainingAmount > 0 ? newRemainingAmount : 0,
          status: newStatus,
          updatedAt: DateTime.now(),
        );

        try {
          // Add payment and update transaction
          await _addPaymentAndUpdateTransactionUseCase(payment, updatedTransaction, paidItems);

          // Update remaining amounts for each paid item
          for (final selectedItem in selectedItemsForTransaction) {
            final transactionItemId = selectedItem.itemWithDetails.transactionItem.id;
            final currentRemainingAmount = selectedItem.remainingAmount;
            final amountBeingPaid = selectedItem.amountToPay;
            final newItemRemainingAmount = currentRemainingAmount - amountBeingPaid;

            // Update the transaction item's remaining amount
            await _ref.read(updateTransactionItemRemainingAmountUseCaseProvider).call(
              transactionItemId,
              newItemRemainingAmount > 0 ? newItemRemainingAmount : 0
            );
          }

          // Recalculate transaction remaining amount based on its items
          await _ref.read(recalculateTransactionRemainingAmountUseCaseProvider).call(transactionId);

          atLeastOnePaymentMade = true;
        } catch (e) {
          // Log the error but continue with other transactions
          debugPrint('Error updating transaction $transactionId: $e');
        }
      }

      // Only refresh if payments were successful
      if (atLeastOnePaymentMade) {
        // Refresh transactions list first
        await refreshTransactions();

        // Then refresh transaction history
        _ref.read(baseTransactionHistoryProvider.notifier).refreshTransactions();
      }

      // Return true only if at least one payment was successfully made
      return atLeastOnePaymentMade;
    } catch (e) {
      debugPrint('Error in addGlobalPayment: $e');
      // Ensure UI is refreshed even on error to show current state
      await refreshTransactions();
      return false;
    }
  }

  // Helper method to select items for payment using a greedy algorithm
  // Prioritizes fully paying items before partial payments
  List<_UnpaidItemInfo> _selectItemsForPayment(List<_UnpaidItemInfo> allItems, double paymentAmount) {
    final selectedItems = <_UnpaidItemInfo>[];
    double remainingPayment = paymentAmount;

    // Sort items by remaining amount (ascending) to prioritize full payments
    final sortedItems = List<_UnpaidItemInfo>.from(allItems);
    sortedItems.sort((a, b) => a.remainingAmount.compareTo(b.remainingAmount));

    // First pass: Fully pay items that can be completely paid
    for (final item in sortedItems) {
      if (item.remainingAmount <= remainingPayment) {
        selectedItems.add(_UnpaidItemInfo(
          transaction: item.transaction,
          itemWithDetails: item.itemWithDetails,
          remainingAmount: item.remainingAmount,
          amountToPay: item.remainingAmount, // Pay the full remaining amount
        ));
        remainingPayment -= item.remainingAmount;

        // If we've allocated the exact amount, we're done
        if (remainingPayment == 0) {
          break;
        }
      }
    }

    // Second pass: If we still have remaining payment, apply it to the next smallest unpaid item
    if (remainingPayment > 0) {
      // Find the next item that hasn't been fully paid yet
      for (final item in sortedItems) {
        if (!selectedItems.any((selected) =>
            selected.itemWithDetails.transactionItem.id == item.itemWithDetails.transactionItem.id)
            && item.remainingAmount > 0) {
          // Create a partial payment for this item
          final partialPaymentAmount = remainingPayment.clamp(0.0, item.remainingAmount);
          selectedItems.add(_UnpaidItemInfo(
            transaction: item.transaction,
            itemWithDetails: item.itemWithDetails,
            remainingAmount: item.remainingAmount,
            amountToPay: partialPaymentAmount, // Clear separation of concerns
          ));
          break; // Only apply to one item for partial payment
        }
      }
    }

    return selectedItems;
  }

  void sortTransactions(String sortBy) {
    if (state is! AsyncData) {
      return;
    }

    final transactionGroups = List<UnpaidTransactionGroup>.from((state as AsyncData<List<UnpaidTransactionGroup>>).value);

    switch (sortBy) {
      case 'date':
        transactionGroups.sort((a, b) => b.transaction.date.compareTo(a.transaction.date));
        break;
      case 'amount':
        transactionGroups.sort((a, b) => b.totalUnpaidAmount.compareTo(a.totalUnpaidAmount));
        break;
      case 'status':
        transactionGroups.sort((a, b) => a.transaction.status.compareTo(b.transaction.status));
        break;
    }

    state = AsyncValue.data(transactionGroups);
  }
}

// Unpaid transactions provider
final unpaidTransactionsProvider = StateNotifierProvider<UnpaidTransactionsNotifier, AsyncValue<List<UnpaidTransactionGroup>>>((ref) {
  return UnpaidTransactionsNotifier(
    getUnpaidTransactionItemsUseCase: ref.watch(getUnpaidTransactionItemsUseCaseProvider),
    getTransactionWithItemsUseCase: ref.watch(getTransactionWithItemsUseCaseProvider),
    addPaymentAndUpdateTransactionUseCase: ref.watch(addPaymentAndUpdateTransactionUseCaseProvider),
    ref: ref,
  );
});

// Transaction with items provider
final transactionWithItemsProvider = FutureProvider.family<TransactionWithItemsEntity, int>((ref, transactionId) async {
  final unpaidTransactionsNotifier = ref.watch(unpaidTransactionsProvider.notifier);
  return unpaidTransactionsNotifier.getTransactionWithItems(transactionId);
});
