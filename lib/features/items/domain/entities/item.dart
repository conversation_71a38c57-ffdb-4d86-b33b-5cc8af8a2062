import 'package:freezed_annotation/freezed_annotation.dart';

part 'item.freezed.dart';
part 'item.g.dart';

@freezed
class ItemEntity with _$ItemEntity {
  const factory ItemEntity({
    required int id,
    required String name,
    required double price,
    required DateTime createdAt,
    required DateTime updatedAt,
  }) = _ItemEntity;

  factory ItemEntity.fromJson(Map<String, dynamic> json) => _$ItemEntityFromJson(json);
}
