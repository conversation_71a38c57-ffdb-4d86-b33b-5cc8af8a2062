import 'package:drift/drift.dart';
import '../../domain/entities/item.dart';
import '../../domain/repositories/item_repository.dart';
import '../../../../core/datasources/local/database/database.dart';
import '../models/item_model.dart';

class ItemRepositoryImpl implements ItemRepository {
  final AppDatabase database;

  ItemRepositoryImpl(this.database);

  @override
  Future<List<ItemEntity>> getAllItems() async {
    final items = await database.getAllItems();
    return items.map((item) => item.toEntity()).toList();
  }

  @override
  Future<List<ItemEntity>> searchItems(String query) async {
    final items = await database.searchItems(query);
    return items.map((item) => item.toEntity()).toList();
  }

  @override
  Future<ItemEntity> getItemById(int id) async {
    final item = await database.getItemById(id);
    return item.toEntity();
  }

  @override
  Future<bool> isItemNameExists(String name) {
    return database.isItemNameExists(name);
  }

  @override
  Future<int> addItem(ItemEntity item) {
    final itemCompanion = ItemsCompanion(
      name: Value(item.name),
      price: Value(item.price),
      createdAt: Value(DateTime.now()),
      updatedAt: Value(DateTime.now()),
    );
    return database.addItem(itemCompanion);
  }

  @override
  Future<bool> updateItem(ItemEntity item) {
    return database.updateItem(item.toCompanion());
  }

  @override
  Future<int> deleteItem(int id) {
    return database.deleteItem(id);
  }
}
