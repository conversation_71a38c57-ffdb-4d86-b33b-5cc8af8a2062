import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../../core/constants/app_constants.dart';
import '../../../../../shared/utils/formatters.dart';
import '../../../../../shared/utils/validators.dart';
import '../../../domain/entities/item.dart';
import '../../providers/items_provider.dart';
import '../../../../../shared/widgets/currency_text_field.dart';
import '../../../../../shared/widgets/custom_text_field.dart';

class AddEditItemBottomSheet extends ConsumerStatefulWidget {
  final ItemEntity? item;

  const AddEditItemBottomSheet({super.key, this.item});

  @override
  ConsumerState<AddEditItemBottomSheet> createState() => _AddEditItemBottomSheetState();
}

class _AddEditItemBottomSheetState extends ConsumerState<AddEditItemBottomSheet> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _priceController = TextEditingController();
  bool _isLoading = false;

  bool get _isEditing => widget.item != null;

  @override
  void initState() {
    super.initState();
    if (_isEditing) {
      _nameController.text = widget.item!.name;
      _priceController.text = widget.item!.price.toString();
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _priceController.dispose();
    super.dispose();
  }

  Future<void> _saveItem() async {
    if (_formKey.currentState?.validate() != true) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    final name = _nameController.text.trim();
    final price = Formatters.parseCurrency(_priceController.text);

    bool success;
    if (_isEditing) {
      success = await ref.read(itemsProvider.notifier).updateItem(widget.item!, price);
    } else {
      // Check if item name already exists
      final nameExists = await ref.read(itemsProvider.notifier).isItemNameExists(name);
      if (nameExists) {
        setState(() {
          _isLoading = false;
        });
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text(AppConstants.errorDuplicateItem)),
          );
        }
        return;
      }

      success = await ref.read(itemsProvider.notifier).addItem(name, price);
    }

    setState(() {
      _isLoading = false;
    });

    if (success && mounted) {
      Navigator.of(context).pop();
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(_isEditing
              ? AppConstants.successItemUpdated
              : AppConstants.successItemAdded),
        ),
      );
    } else if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to ${_isEditing ? 'update' : 'add'} item'),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Form(
        key: _formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Text(
              _isEditing ? AppConstants.titleEditItem : AppConstants.titleAddItem,
              style: Theme.of(context).textTheme.titleLarge,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            CustomTextField(
              controller: _nameController,
              label: AppConstants.labelItemName,
              validator: Validators.validateItemName,
              textCapitalization: TextCapitalization.words,
              readOnly: _isEditing, // Name cannot be edited
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            CurrencyTextField(
              controller: _priceController,
              label: AppConstants.labelItemPrice,
              validator: Validators.validatePrice,
            ),
            const SizedBox(height: AppConstants.largePadding),
            ElevatedButton(
              onPressed: _isLoading ? null : _saveItem,
              child: _isLoading
                  ? const SizedBox(
                      height: 20,
                      width: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                      ),
                    )
                  : Text(_isEditing ? AppConstants.buttonUpdate : AppConstants.buttonAdd),
            ),
            const SizedBox(height: AppConstants.smallPadding),
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text(AppConstants.buttonCancel),
            ),
          ],
        ),
      ),
    );
  }
}
