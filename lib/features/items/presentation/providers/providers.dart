import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/datasources/local/database/database.dart';
import '../../data/repositories/item_repository_impl.dart';
import '../../domain/repositories/item_repository.dart';
import '../../domain/usecases/item_usecases.dart';

// Database provider
final databaseProvider = Provider<AppDatabase>((ref) {
  return AppDatabase();
});

// Repository providers
final itemRepositoryProvider = Provider<ItemRepository>((ref) {
  final database = ref.watch(databaseProvider);
  return ItemRepositoryImpl(database);
});

// Item use case providers
final getAllItemsUseCaseProvider = Provider<GetAllItemsUseCase>((ref) {
  final repository = ref.watch(itemRepositoryProvider);
  return GetAllItemsUseCase(repository);
});

final searchItemsUseCaseProvider = Provider<SearchItemsUseCase>((ref) {
  final repository = ref.watch(itemRepositoryProvider);
  return SearchItemsUseCase(repository);
});

final getItemByIdUseCaseProvider = Provider<GetItemByIdUseCase>((ref) {
  final repository = ref.watch(itemRepositoryProvider);
  return GetItemByIdUseCase(repository);
});

final isItemNameExistsUseCaseProvider = Provider<IsItemNameExistsUseCase>((ref) {
  final repository = ref.watch(itemRepositoryProvider);
  return IsItemNameExistsUseCase(repository);
});

final addItemUseCaseProvider = Provider<AddItemUseCase>((ref) {
  final repository = ref.watch(itemRepositoryProvider);
  return AddItemUseCase(repository);
});

final updateItemUseCaseProvider = Provider<UpdateItemUseCase>((ref) {
  final repository = ref.watch(itemRepositoryProvider);
  return UpdateItemUseCase(repository);
});

final deleteItemUseCaseProvider = Provider<DeleteItemUseCase>((ref) {
  final repository = ref.watch(itemRepositoryProvider);
  return DeleteItemUseCase(repository);
});
